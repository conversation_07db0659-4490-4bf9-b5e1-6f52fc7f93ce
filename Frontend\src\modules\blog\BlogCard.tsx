import { Link } from "react-router-dom";
import { Calendar, Clock, ArrowRight, BookO<PERSON>, Eye } from "lucide-react";
import { useLanguage } from "@/core/providers/LanguageContext";
import { cn } from "@/core/utils/utils";
import { useClickTracking } from "@/core/providers/ClickTrackingContext";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

interface BlogCardProps {
  title: string;
  excerpt: string;
  image: string;
  date: string;
  readTime: string;
  category: string;
  href: string;
  url?: string;
  onClick?: () => void;
  index?: number;
}

const BlogCard = ({
  title,
  excerpt,
  image,
  date,
  readTime,
  category,
  href,
  url,
  onClick,
  index = 0,
}: BlogCardProps) => {
  const { t, isRTL } = useLanguage();
  const { trackClick } = useClickTracking();
  const isExternal = url && url.startsWith('http');
  const [isHovered, setIsHovered] = useState(false);

  const cardContent = (
    <motion.div
      className="relative group cursor-pointer max-w-2xl mx-auto"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative h-96 rounded-3xl overflow-hidden bg-gradient-to-br from-brand-purple-900 via-brand-purple-800 to-brand-black shadow-2xl group-hover:shadow-3xl transition-all duration-500">
        {/* Background Image */}
        <img
          src={image}
          alt={title}
          className="absolute inset-0 w-full h-full object-cover opacity-40 group-hover:opacity-60 transition-opacity duration-500"
          loading="lazy"
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-brand-purple-900/90 via-brand-purple-800/60 to-transparent" />

        {/* Category Badge */}
        <div className="absolute top-6 left-6 z-20">
          <span className="bg-brand-purple-600/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-semibold">
            {category}
          </span>
        </div>

        {/* Content */}
        <div className="relative z-10 p-8 h-full flex flex-col justify-between">
          {/* Title - Always visible */}
          <div>
            <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-brand-purple-200 transition-colors duration-300 leading-tight">
              {title}
            </h3>
          </div>

          {/* Hover Content - Revealed on hover */}
          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                className="bg-brand-purple-900/80 backdrop-blur-sm rounded-xl p-6 border border-brand-purple-500/30"
              >
                <p className="text-brand-purple-100 leading-relaxed mb-4">
                  {excerpt}
                </p>

                {/* Meta Information */}
                <div className="flex items-center justify-between text-sm text-brand-purple-300 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{date}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{readTime}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>Read More</span>
                  </div>
                </div>

                {/* CTA Button */}
                <div className="flex items-center justify-between">
                  <span className="text-brand-purple-300 text-xs font-semibold">
                    Click to read full article
                  </span>
                  <ArrowRight className="w-5 h-5 text-brand-purple-300 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 rounded-3xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/50 transition-all duration-500" />

        {/* Shimmer effect */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent shimmer-effect" />
        </div>
      </div>
    </motion.div>
  );

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
    trackClick({ pageName: 'Blog', postTitle: title });
  };
    <ArrowRight
      className={cn(
        "h-4 w-4 group-hover:transition-transform duration-300",
        isRTL
          ? "mr-2 group-hover:-translate-x-2 rotate-180"
          : "ml-2 group-hover:translate-x-2"
      )}
    />
  </Link>
</div>

      </div>
    </>
  );

  const handleClick = (e: React.MouseEvent) => {
    trackClick({
      pageName: 'Blog List',
      blogTitle: title,
      buttonLabel: 'Read Article',
    });
    if (onClick) onClick();
  };

  return url ? (
    <a href={url} className={cardClass} target="_blank" rel="noopener noreferrer" onClick={handleClick}>
      {cardContent}
    </a>
  ) : (
    <Link to={href || '#'} className={cardClass} onClick={handleClick}>
  {cardContent}
</Link>

  );
};

export default BlogCard;