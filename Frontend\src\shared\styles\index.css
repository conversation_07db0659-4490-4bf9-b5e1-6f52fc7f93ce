@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for auth modal */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-out;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  html[dir="rtl"] {
    direction: rtl;
  }

  html[dir="ltr"] {
    direction: ltr;
  }

  body {
    @apply bg-brand-black text-brand-white font-sans antialiased;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    /* Dynamic background images are handled by DynamicBackground component */
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display font-semibold tracking-tight;
  }

  h1 {
    @apply text-display-lg;
  }

  h2 {
    @apply text-display-md;
  }

  h3 {
    @apply text-display-sm;
  }
}

@layer components {
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-brand-white to-brand-grey-300 bg-clip-text text-transparent;
  }

  .btn-primary {
    @apply bg-brand-white text-brand-black hover:bg-brand-grey-100 transition-all duration-200 font-medium px-6 py-3 rounded-lg;
  }

  .btn-secondary {
    @apply bg-transparent border border-brand-grey-500 text-brand-white hover:bg-brand-grey-900 hover:border-brand-grey-300 transition-all duration-200 font-medium px-6 py-3 rounded-lg;
  }

  .card-hover {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
  }

  .blur-background {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* RTL Utilities */
  [dir="rtl"] .rtl\:text-right {
    text-align: right;
  }

  [dir="rtl"] .rtl\:text-left {
    text-align: left;
  }

  [dir="rtl"] .rtl\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  [dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .rtl\:ml-auto {
    margin-left: auto;
  }

  [dir="rtl"] .rtl\:mr-auto {
    margin-right: auto;
  }

  /* Arabic Font Optimization */
  [dir="rtl"] {
    font-feature-settings: "liga", "kern", "calt";
  }

  /* Improved readability for Arabic text */
  [dir="rtl"] h1,
  [dir="rtl"] h2,
  [dir="rtl"] h3,
  [dir="rtl"] h4,
  [dir="rtl"] h5,
  [dir="rtl"] h6 {
    line-height: 1.4;
  }

  [dir="rtl"] p {
    line-height: 1.7;
  }

  /* ReactQuill Editor Styling - Dark Theme */
  .ql-editor {
    color: #fff !important;
    background-color: #1f2937 !important;
    font-size: 14px;
    line-height: 1.6;
    border: 1px solid #374151 !important;
    border-radius: 0.375rem;
  }
  .ql-editor p,
  .ql-editor h1,
  .ql-editor h2,
  .ql-editor h3,
  .ql-editor h4,
  .ql-editor h5,
  .ql-editor h6,
  .ql-editor strong,
  .ql-editor b,
  .ql-editor em,
  .ql-editor i,
  .ql-editor ul,
  .ql-editor ol,
  .ql-editor li,
  .ql-editor blockquote {
    color: #fff !important;
  }
  .ql-editor a {
    color: #60a5fa !important;
  }
  .ql-editor.ql-blank::before {
    color: #9ca3af !important;
    font-style: italic;
  }
  /* Quill toolbar styling for dark theme */
  .ql-toolbar.ql-snow {
    background-color: #374151 !important;
    border: 1px solid #4b5563 !important;
    border-radius: 0.375rem 0.375rem 0 0;
  }
  .ql-container.ql-snow {
    border: 1px solid #4b5563 !important;
    border-top: none !important;
    border-radius: 0 0 0.375rem 0.375rem;
  }
}
