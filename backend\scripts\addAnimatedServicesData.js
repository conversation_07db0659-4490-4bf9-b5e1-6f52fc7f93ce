import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Service from '../src/components/service/serviceModel.js';

// Load environment variables
dotenv.config();

const animatedServicesData = [
  {
    title: 'Digital Marketing Strategy',
    description: 'Comprehensive digital marketing solutions powered by AI and data analytics to maximize your online presence and drive conversions.',
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
    slug: 'digital-marketing-strategy',
    subServices: [
      {
        title: 'Social Media Management',
        description: 'Complete social media strategy and management across all platforms. We create engaging content, manage your community, run targeted campaigns, and provide detailed analytics to grow your online presence.',
        image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop'
      },
      {
        title: 'Content Marketing',
        description: 'Strategic content creation and distribution to attract, engage, and convert your target audience. From blog posts to video content, we craft compelling narratives that drive results.',
        image: 'https://images.unsplash.com/photo-1542435503-956c469947f6?w=500&h=300&fit=crop'
      },
      {
        title: 'Email Marketing Automation',
        description: 'Automated email campaigns that nurture leads and drive conversions. Personalized messaging, segmentation, and performance tracking to maximize ROI.',
        image: 'https://images.unsplash.com/photo-1596526131083-e8c633c948d2?w=500&h=300&fit=crop'
      }
    ]
  },
  {
    title: 'SEO & Analytics',
    description: 'Advanced SEO strategies and comprehensive analytics to boost your search rankings and understand your audience behavior.',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
    slug: 'seo-analytics',
    subServices: [
      {
        title: 'Technical SEO Optimization',
        description: 'Complete technical SEO audit and optimization including site speed, mobile responsiveness, schema markup, and crawlability improvements.',
        image: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=500&h=300&fit=crop'
      },
      {
        title: 'Keyword Research & Strategy',
        description: 'In-depth keyword research and competitive analysis to identify high-value opportunities and create content strategies that rank.',
        image: 'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=500&h=300&fit=crop'
      },
      {
        title: 'Analytics & Reporting',
        description: 'Comprehensive analytics setup and custom reporting dashboards to track performance, user behavior, and conversion metrics.',
        image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop'
      }
    ]
  },
  {
    title: 'AI-Powered Solutions',
    description: 'Cutting-edge artificial intelligence solutions to automate processes, enhance customer experience, and drive business growth.',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop',
    slug: 'ai-powered-solutions',
    subServices: [
      {
        title: 'Chatbot Development',
        description: 'Intelligent chatbots powered by natural language processing to provide 24/7 customer support and lead qualification.',
        image: 'https://images.unsplash.com/photo-1531746790731-6c087fecd65a?w=500&h=300&fit=crop'
      },
      {
        title: 'Predictive Analytics',
        description: 'Machine learning models to predict customer behavior, optimize pricing strategies, and forecast business trends.',
        image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=300&fit=crop'
      },
      {
        title: 'Process Automation',
        description: 'AI-driven automation solutions to streamline workflows, reduce manual tasks, and improve operational efficiency.',
        image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=500&h=300&fit=crop'
      }
    ]
  },
  {
    title: 'Web Development',
    description: 'Modern, responsive web applications built with the latest technologies to deliver exceptional user experiences.',
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',
    slug: 'web-development',
    subServices: [
      {
        title: 'Frontend Development',
        description: 'Modern, responsive user interfaces built with React, Vue, or Angular. Focus on performance, accessibility, and user experience.',
        image: 'https://images.unsplash.com/photo-1547658719-da2b51169166?w=500&h=300&fit=crop'
      },
      {
        title: 'Backend Development',
        description: 'Scalable server-side solutions with robust APIs, database optimization, and cloud infrastructure setup.',
        image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop'
      },
      {
        title: 'E-commerce Solutions',
        description: 'Complete e-commerce platforms with payment integration, inventory management, and conversion optimization.',
        image: 'https://images.unsplash.com/photo-1556742111-a301076d9d18?w=500&h=300&fit=crop'
      }
    ]
  }
];

const addAnimatedServicesData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing services
    await Service.deleteMany({});
    console.log('Cleared existing services');

    // Add new animated services data
    const createdServices = await Service.insertMany(animatedServicesData);
    console.log(`Added ${createdServices.length} services with animations and sub-services`);

    console.log('Animated services data added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

addAnimatedServicesData();
