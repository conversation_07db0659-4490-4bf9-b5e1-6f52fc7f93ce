import { ReactNode } from "react";
import Navigation from "./Navigation";
import Footer from "./Footer";
import ScrollToTop from "./ScrollToTop";
import DynamicBackground from "./DynamicBackground";

interface LayoutProps {
  children: ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="min-h-screen relative flex flex-col">
      <DynamicBackground interval={1000} />
      <Navigation />
      <main className="flex-1 pt-24 pb-16 px-2 md:px-0 transition-all duration-300 relative z-10">
        {children}
      </main>
      <Footer />
      <ScrollToTop />
    </div>
  );
};

export default Layout;
