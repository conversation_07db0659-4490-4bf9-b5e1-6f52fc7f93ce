import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, FileText, Sparkles, BookOpen, TrendingUp, Clock } from "lucide-react";
import { getBlogPosts } from "@/shared/services/blog.service";
import { BlogPost } from "@/core/types";
import BlogCard from "@/modules/blog/BlogCard";
import { useClickTracking } from "@/core/providers/ClickTrackingContext";
import { useLanguage } from "@/core/providers/LanguageContext";
import { useAutoScroll } from "@/shared/hooks/use-auto-scroll";
import { motion, AnimatePresence } from "framer-motion";

const Blog = () => {
  const { trackClick } = useClickTracking();
  const { language, t } = useLanguage();
  const { scrollToTop } = useAutoScroll();
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    scrollToTop(); // Scroll to top when component mounts
    fetchBlogs();
  }, [language, scrollToTop]);

  const fetchBlogs = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await getBlogPosts({ language });
      setBlogs(res.data);
    } catch (err) {
      setError("Failed to fetch blogs");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pt-20 min-h-screen relative">
      <div className="section-padding">
        <div className="container-custom">
          {/* Enhanced Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto mb-16"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-3xl mb-8 glow-effect">
              <BookOpen className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold gradient-text mb-6">
              {t("blog.title")}
            </h1>
            <p className="text-xl text-brand-purple-200 mb-10 leading-relaxed">
              {t("blog.subtitle")}
            </p>
            <div className="w-32 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto rounded-full" />
          </motion.div>
          {loading ? (
            <div className="text-center text-brand-purple-300 py-12">
              <div className="animate-spin w-8 h-8 border-4 border-brand-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
              {t("common.loading")}
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-12 bg-red-500/10 rounded-2xl border border-red-500/20">
              <p className="mb-4">{error}</p>
              <button onClick={fetchBlogs} className="btn-primary">
                Try Again
              </button>
            </div>
          ) : blogs.length === 0 ? (
            <div className="text-center text-brand-purple-300 py-12">
              <BookOpen className="w-16 h-16 mx-auto mb-4 text-brand-purple-500" />
              <p>No blog posts found.</p>
            </div>
          ) : (
            <div className="space-y-16">
              {blogs.map((post, index) => (
                <motion.div
                  key={post.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-8 items-center`}
                >
                  <BlogCard
                    title={post.title}
                    excerpt={post.excerpt}
                    image={post.image || "/public/placeholder.svg"}
                    date={post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : new Date(post.createdAt).toLocaleDateString()}
                    readTime={"5 min read"}
                    category={post.category || "General"}
                    href={`/blog/${post.slug || post.id}`}
                    url={post.url}
                    onClick={() => trackClick({ pageName: 'Blog', postTitle: post.title })}
                    index={index}
                  />
                </motion.div>
              ))}
            </div>
          )}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mt-16"
          >
            <Link to="/" className="bg-gradient-to-r from-brand-purple-600 to-brand-purple-700 hover:from-brand-purple-700 hover:to-brand-purple-800 text-white px-8 py-4 rounded-lg font-semibold flex items-center justify-center transition-all duration-300 glow-effect">
              {t("nav.home")}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
            <Link to="/contact" className="bg-transparent border-2 border-brand-purple-500 text-brand-purple-300 hover:bg-brand-purple-600 hover:text-white hover:border-brand-purple-400 px-8 py-4 rounded-lg font-semibold transition-all duration-300">
              {t("nav.contact")}
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Blog;