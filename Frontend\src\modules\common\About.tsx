import { <PERSON> } from "react-router-dom";
import {
  Users,
  Target,
  Award,
  Lightbulb,
  Heart,
  Shield,
  ArrowRight,
  CheckCircle,
  Quote,
} from "lucide-react";

const About = () => {
  const teamMembers = [
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CEO & Founder",
      image:"https://icheck24.com/wp-content/uploads/2024/11/Taswirti.jpg",
      bio: "Visionary leader with 10+ years in digital marketing, driving company growth and strategic innovation to help businesses achieve exceptional results.",
    },
   
  ];

  const values = [
    {
      icon: <Heart size={20} />,
      title: "Client-Centric",
      description: "We put our clients' success at the heart of everything we do, building lasting partnerships based on trust and results."
    },
    {
      icon: <Lightbulb size={20} />,
      title: "Innovation",
      description: "We stay ahead of digital trends and continuously evolve our strategies to deliver cutting-edge solutions."
    },
    {
      icon: <Shield size={20} />,
      title: "Integrity",
      description: "We believe in transparent communication, honest reporting, and ethical business practices in all our relationships."
    },
    {
      icon: <Award size={20} />,
      title: "Excellence",
      description: "We strive for perfection in every campaign, creative piece, and strategic recommendation we deliver."
    },
    {
      icon: <Users size={20} />,
      title: "Collaboration",
      description: "We work as an extension of your team, fostering open communication and shared success."
    },
    {
      icon: <CheckCircle size={20} />,
      title: "Results-Driven",
      description: "We focus on measurable outcomes and data-driven insights to ensure every investment delivers value."
    }
  ];

  

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="section-padding bg-brand-black">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-brand-white mb-6">
              About <span className="gradient-text">Click ForYou</span>
            </h1>
            <p className="text-xl text-brand-grey-300 leading-relaxed">
              We're a forward-thinking digital marketing agency dedicated to
              transforming brands through innovative strategies, creative
              excellence, and data-driven results.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-2xl p-8 text-center">
              <div className="text-6xl mb-4">🚀</div>
              <h3 className="text-2xl font-bold text-white mb-2">Our Vision</h3>
              <p className="text-brand-purple-200">Transforming businesses through innovative digital solutions</p>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-brand-white mb-6">
                Our Story
              </h2>
              <p className="text-brand-grey-300 mb-4 leading-relaxed">
                Founded in 2020, Click ForYou emerged from a simple yet powerful
                belief: every business deserves exceptional digital marketing
                that drives real growth. Our founders, seasoned marketing
                professionals, recognized the need for an agency that combines
                creative innovation with strategic thinking.
              </p>
              <p className="text-brand-grey-300 mb-6 leading-relaxed">
                Today, we're proud to serve over 150 clients worldwide, helping
                them navigate the complex digital landscape and achieve their
                business objectives through tailored marketing solutions.
              </p>
              <div className="flex items-center space-x-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-white">4+</div>
                  <div className="text-brand-grey-400 text-sm">Years</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-white">
                    150+
                  </div>
                  <div className="text-brand-grey-400 text-sm">Clients</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-brand-white">
                    500+
                  </div>
                  <div className="text-brand-grey-400 text-sm">Campaigns</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="section-padding bg-brand-grey-900">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="bg-brand-black border border-brand-grey-700 rounded-2xl p-8">
              <div className="w-14 h-14 bg-brand-white rounded-lg flex items-center justify-center mb-6">
                <Target className="text-brand-black" size={24} />
              </div>
              <h3 className="text-2xl font-bold text-brand-white mb-4">
                Our Mission
              </h3>
              <p className="text-brand-grey-300 leading-relaxed">
                To empower businesses of all sizes with cutting-edge digital
                marketing strategies that drive measurable growth, enhance brand
                visibility, and create lasting connections with their target
                audiences.
              </p>
            </div>

            <div className="bg-brand-black border border-brand-grey-700 rounded-2xl p-8">
              <div className="w-14 h-14 bg-brand-white rounded-lg flex items-center justify-center mb-6">
                <Lightbulb className="text-brand-black" size={24} />
              </div>
              <h3 className="text-2xl font-bold text-brand-white mb-4">
                Our Vision
              </h3>
              <p className="text-brand-grey-300 leading-relaxed">
                To be the world's most trusted digital marketing partner,
                recognized for our innovative approaches, exceptional results,
                and unwavering commitment to client success in an ever-evolving
                digital landscape.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="section-padding bg-brand-black">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-brand-white mb-6">
              Our Values
            </h2>
            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              These core principles guide everything we do and shape how we work
              with our clients and each other.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="bg-brand-grey-900 border border-brand-grey-700 rounded-xl p-6 hover:border-brand-grey-500 transition-all duration-300"
              >
                <div className="w-12 h-12 bg-brand-white rounded-lg flex items-center justify-center mb-4">
                  <div className="text-brand-black">{value.icon}</div>
                </div>
                <h3 className="text-xl font-bold text-brand-white mb-3">
                  {value.title}
                </h3>
                <p className="text-brand-grey-300 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section id="team" className="section-padding bg-brand-grey-900">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-brand-white mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              Our diverse team of experts brings together decades of experience
              in digital marketing, strategy, and creative excellence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="text-center group">
                <div className="relative mb-6">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full aspect-square object-cover rounded-xl group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-brand-black/20 rounded-xl group-hover:bg-brand-black/40 transition-all duration-300"></div>
                </div>
                <h3 className="text-xl font-bold text-brand-white mb-2">
                  {member.name}
                </h3>
                <p className="text-brand-grey-300 font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-brand-grey-400 text-sm leading-relaxed">
                  {member.bio}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="section-padding bg-brand-black">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-brand-white mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              From humble beginnings to industry recognition, here's how we've
              grown and evolved.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
           
          </div>
        </div>
      </section>

     
      {/* CTA Section */}
      <section className="section-padding bg-brand-black">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-5xl font-bold text-brand-white mb-6">
              Ready to Work with Us?
            </h2>
            <p className="text-xl text-brand-grey-300 mb-10 leading-relaxed">
              Let's discuss how our expertise can help transform your digital
              marketing and drive measurable results for your business.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact" className="btn-primary text-lg px-8 py-4">
                Start Your Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link to="/services" className="btn-secondary text-lg px-8 py-4">
                Explore Our Services
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
