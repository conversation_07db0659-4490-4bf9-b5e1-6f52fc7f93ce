import { Link } from "react-router-dom";
import Hero from "@/shared/components/Hero";
import ServiceCard from "@/modules/services/ServiceCard";
import BlogCard from "@/modules/blog/BlogCard";
import AnimatedSection from "@/shared/components/AnimatedSection";
import { useLanguage } from "@/core/providers/LanguageContext";
import { cn } from "@/core/utils/utils";
import { memo, useEffect, useState } from "react";
import { getBlogPosts } from "@/shared/services/blog.service";
import { getServices } from "@/shared/services/service.service";
import { BlogPost, Service } from "@/core/types";
import { ServiceCardSkeleton, BlogCardSkeleton } from "@/shared/components/SkeletonLoader";

const Index = memo(() => {
  const { t, isRTL } = useLanguage();
  // State for services from database
  const [services, setServices] = useState<Service[]>([]);
  const [servicesLoading, setServicesLoading] = useState(false);
  const [servicesError, setServicesError] = useState<string | null>(null);

  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [blogsLoading, setBlogsLoading] = useState(false);
  const [blogsError, setBlogsError] = useState<string | null>(null);

  useEffect(() => {
    fetchServices();
    fetchBlogs();
  }, []);

  const fetchServices = async () => {
    setServicesLoading(true);
    setServicesError(null);
    try {
      const response = await getServices({ limit: 6 }); // Limit to 6 services for homepage
      setServices(response.data);
    } catch (err) {
      setServicesError("Failed to fetch services");
      console.error("Error fetching services:", err);
    } finally {
      setServicesLoading(false);
    }
  };

  const fetchBlogs = async () => {
    setBlogsLoading(true);
    setBlogsError(null);
    try {
      const response = await getBlogPosts({ limit: 3 });
      setBlogs(response.data);
    } catch (err) {
      setBlogsError("Failed to fetch blog posts");
    } finally {
      setBlogsLoading(false);
    }
  };



  const features = [
    t("about.feature1"),
    t("about.feature2"),
    t("about.feature3"),
    t("about.feature4"),
    t("about.feature5"),
    t("about.feature6"),
  ];

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <Hero />

      {/* About Preview Section */}
      <AnimatedSection
        id="about-section"
        className="section-padding bg-brand-grey-900"
        animation="fade-in"
      >
        <div className="container-custom">
          <div
            className={cn(
              "grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",
              isRTL && "lg:grid-cols-2",
            )}
          >
            <div className={isRTL ? "lg:order-2" : ""}>
              <h2
                className={cn(
                  "text-3xl md:text-4xl font-bold text-brand-white mb-6",
                  isRTL && "text-right",
                )}
              >
                {t("about.title")}
              </h2>
              <p
                className={cn(
                  "text-lg text-brand-grey-300 mb-6 leading-relaxed",
                  isRTL && "text-right",
                )}
              >
                {t("about.description1")}
              </p>
              <p
                className={cn(
                  "text-brand-grey-300 mb-8 leading-relaxed",
                  isRTL && "text-right",
                )}
              >
                {t("about.description2")}
              </p>

              {/* Features Grid */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className={cn(
                      "flex items-center",
                      isRTL && "flex-row-reverse",
                    )}
                  >
                    <span className="text-brand-grey-300">{feature}</span>
                  </div>
                ))}
              </div>

              <Link
                to="/about"
                className={cn(
                  "btn-primary flex items-center",
                  isRTL && "flex-row-reverse",
                )}
              >
                {t("about.cta")}
                <span className={cn("h-5 w-5", isRTL ? "mr-2 rotate-180" : "ml-2")}>
                  &rarr;
                </span>
              </Link>
            </div>
          </div>
        </div>
      </AnimatedSection>

      {/* Stats Section */}
      <AnimatedSection className="py-16 bg-brand-black" animation="slide-up">
        <div className="container-custom">
         
        </div>
      </AnimatedSection>

      {/* Services Section */}
      <AnimatedSection
        id="services-section"
        className="section-padding bg-gradient-to-br from-brand-black via-brand-grey-950 to-brand-purple-950"
        animation="fade-in"
      >
        <div className="container-custom">
          <div className={cn("text-center mb-16", isRTL && "text-right")}>
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-2xl mb-6 glow-effect">
              <span className="text-2xl">✨</span>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold gradient-text mb-6">
              {t("services.title")}
            </h2>
            <p className="text-xl text-brand-purple-200 max-w-3xl mx-auto leading-relaxed">
              {t("services.subtitle")}
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto mt-6 rounded-full" />
          </div>

          {servicesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }).map((_, index) => (
                <ServiceCardSkeleton key={index} />
              ))}
            </div>
          ) : servicesError ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">{servicesError}</p>
              <button
                onClick={fetchServices}
                className="btn-secondary"
              >
                Try Again
              </button>
            </div>
          ) : services.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-brand-grey-400">No services available at the moment.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <AnimatedSection
                  key={service.id}
                  animation="scale-in"
                  delay={index * 200}
                  className="h-full"
                >
                  <ServiceCard
                    title={service.title}
                    description={service.description}
                    image={service.image}
                    href={`/services/${service.slug || service.id}`}
                  />
                </AnimatedSection>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              to="/services"
              className={cn(
                "btn-secondary flex items-center justify-center",
                isRTL && "flex-row-reverse",
              )}
            >
              {t("services.viewAll")}
              <span className={cn("h-5 w-5", isRTL ? "mr-2 rotate-180" : "ml-2")}>
                &rarr;
              </span>
            </Link>
          </div>
        </div>
      </AnimatedSection>

      {/* Blog Preview Section */}
      <AnimatedSection
        id="blog-section"
        className="section-padding bg-brand-grey-900"
        animation="slide-up"
      >
        <div className="container-custom">
          <div className={cn("text-center mb-16", isRTL && "text-right")}>
            <h2 className="text-3xl md:text-4xl font-bold text-brand-white mb-6">
              {t("blog.title")}
            </h2>
            <p className="text-xl text-brand-grey-300 max-w-3xl mx-auto">
              {t("blog.subtitle")}
            </p>
          </div>
          {blogsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 3 }).map((_, index) => (
                <BlogCardSkeleton key={index} />
              ))}
            </div>
          ) : blogsError ? (
            <div className="text-center text-red-500 py-12">{blogsError}</div>
          ) : blogs.length === 0 ? (
            <div className="text-center text-brand-grey-400 py-12">No blog posts found.</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogs.map((post, index) => (
                <AnimatedSection
                  key={post.id}
                  animation="fade-in"
                  delay={index * 150}
                  className="h-full"
                >
                  <BlogCard
                    title={post.title}
                    excerpt={post.excerpt}
                    image={post.image || "/public/placeholder.svg"}
                    date={post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : new Date(post.createdAt).toLocaleDateString()}
                    readTime={"5 min read"}
                    category={post.category || "General"}
                    href={`/blog/${post.slug || post.id}`}
                  />
                </AnimatedSection>
              ))}
            </div>
          )}
          <div className="text-center mt-12">
            <Link
              to="/blog"
              className={cn(
                "btn-secondary flex items-center justify-center",
                isRTL && "flex-row-reverse",
              )}
            >
              {t("blog.readMore")}
              <span className={cn("h-5 w-5", isRTL ? "mr-2 rotate-180" : "ml-2")}>
                &rarr;
              </span>
            </Link>
          </div>
        </div>
      </AnimatedSection>

      {/* CTA Section */}
      <AnimatedSection
        id="cta-section"
        className="section-padding bg-brand-black"
        animation="scale-in"
      >
        <div className="container-custom">
          <div
            className={cn(
              "text-center max-w-4xl mx-auto",
              isRTL && "text-right",
            )}
          >
            <h2 className="text-3xl md:text-5xl font-bold text-brand-white mb-6">
              {t("cta.title")}
              <span className="gradient-text">{t("cta.titleHighlight")}</span>
            </h2>
            <p className="text-xl text-brand-grey-300 mb-10 leading-relaxed">
              {t("cta.subtitle")}
            </p>

            <div
              className={cn(
                "flex flex-col sm:flex-row gap-4 justify-center",
                isRTL && "sm:flex-row-reverse",
              )}
            >
              <Link
                to="/contact"
                className={cn(
                  "btn-primary text-lg px-8 py-4 hover:shadow-xl hover:shadow-brand-white/20 transition-all duration-300 flex items-center justify-center",
                  isRTL && "flex-row-reverse",
                )}
              >
                {t("cta.primary")}
                <span className={cn("h-5 w-5", isRTL ? "mr-2 rotate-180" : "ml-2")}>
                  &rarr;
                </span>
              </Link>
              <Link
                to="/about"
                className="btn-secondary text-lg px-8 py-4 hover:shadow-xl hover:shadow-brand-grey-500/20 transition-all duration-300"
              >
                {t("cta.secondary")}
              </Link>
            </div>
          </div>
        </div>
      </AnimatedSection>
    </div>
  );
});

Index.displayName = "Index";

export default Index;
