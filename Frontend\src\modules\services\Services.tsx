import { useEffect, useState } from "react";
import { getServices } from "@/shared/services/service.service";
import { Service, SubService } from "@/core/types";
import { useClickTracking } from "@/core/providers/ClickTrackingContext";
import { useAutoScroll } from "@/shared/hooks/use-auto-scroll";
import { ChevronDown, ArrowRight, Sparkles, Zap, Target, TrendingUp, Star } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const Services = () => {
  const { trackClick } = useClickTracking();
  const { scrollToTop } = useAutoScroll();
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [hoveredSubService, setHoveredSubService] = useState<string | null>(null);

  useEffect(() => {
    scrollToTop();
    fetchServices();
  }, []);

  const fetchServices = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await getServices();
      setServices(res.data);
    } catch (err) {
      setError("Failed to fetch services");
    } finally {
      setLoading(false);
    }
  };

  // Service icons mapping
  const getServiceIcon = (index: number) => {
    const icons = [Sparkles, Zap, Target, TrendingUp, Star, ArrowRight];
    const IconComponent = icons[index % icons.length];
    return IconComponent;
  };

  // Enhanced SubService Card with hover reveal and proper image display
  const SubServiceCard = ({ subService, serviceName, index }: { 
    subService: SubService; 
    serviceName: string; 
    index: number;
  }) => {
    const isHovered = hoveredSubService === `${serviceName}-${index}`;
    
    return (
      <motion.div
        initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        className="relative group cursor-pointer"
        onMouseEnter={() => setHoveredSubService(`${serviceName}-${index}`)}
        onMouseLeave={() => setHoveredSubService(null)}
        onClick={() => trackClick({
          pageName: 'Services',
          serviceName: serviceName,
          subServiceName: subService.title,
          buttonLabel: 'Sub-Service Card',
        })}
      >
        <div className="relative h-80 rounded-2xl overflow-hidden bg-gradient-to-br from-brand-purple-900 via-brand-purple-800 to-brand-black shadow-xl">
          {/* Background Image from Database */}
          {subService.image && (
            <img
              src={subService.image}
              alt={subService.title}
              className="absolute inset-0 w-full h-full object-cover opacity-30 group-hover:opacity-50 transition-opacity duration-500"
            />
          )}
          
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-brand-purple-900/90 via-brand-purple-800/60 to-transparent" />
          
          {/* Content */}
          <div className="relative z-10 p-6 h-full flex flex-col justify-between">
            {/* Title - Always visible */}
            <div>
              <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-brand-purple-200 transition-colors duration-300">
                {subService.title}
              </h3>
            </div>
            
            {/* Description - Revealed on hover */}
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-brand-purple-900/80 backdrop-blur-sm rounded-xl p-4 border border-brand-purple-500/30"
                >
                  <p className="text-brand-purple-100 leading-relaxed text-sm">
                    {subService.description}
                  </p>
                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-brand-purple-300 text-xs font-semibold">
                      Click to explore
                    </span>
                    <ArrowRight className="w-4 h-4 text-brand-purple-300" />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          
          {/* Glow effect */}
          <div className="absolute inset-0 rounded-2xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/50 transition-all duration-500" />
        </div>
      </motion.div>
    );
  };

  return (
    <div className="pt-20 min-h-screen relative">
      <div className="section-padding">
        <div className="container-custom">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto mb-16"
          >
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-brand-purple-600 to-brand-purple-800 rounded-3xl mb-8 glow-effect">
              <Sparkles className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold gradient-text mb-6">
              Our Services
            </h1>
            <p className="text-xl text-brand-grey-300 mb-10 leading-relaxed">
              Discover our comprehensive digital solutions designed to transform your business
            </p>
            <div className="w-32 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto rounded-full" />
          </motion.div>

          {loading ? (
            <div className="text-center text-brand-grey-400 py-12">
              <div className="animate-spin w-8 h-8 border-4 border-brand-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
              Loading services...
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-12 bg-red-500/10 rounded-2xl border border-red-500/20">
              <p className="mb-4">{error}</p>
              <button onClick={fetchServices} className="btn-primary">
                Try Again
              </button>
            </div>
          ) : services.length === 0 ? (
            <div className="text-center text-brand-grey-400 py-12">No services found.</div>
          ) : (
            <div className="space-y-24">
              {services.map((service, index) => (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-12 items-center`}
                >
                  {/* Service Main Card */}
                  <div className="flex-1 max-w-2xl">
                    <div
                      className="relative group cursor-pointer"
                      onClick={() => {
                        setSelectedService(selectedService === service.id ? null : service.id);
                        trackClick({
                          pageName: 'Services',
                          serviceName: service.title,
                          buttonLabel: 'Service Toggle',
                        });
                      }}
                    >
                      <div className="relative h-96 rounded-3xl overflow-hidden bg-gradient-to-br from-brand-purple-900 via-brand-purple-800 to-brand-black shadow-2xl group-hover:shadow-3xl transition-all duration-500">
                        {/* Background Image from Database */}
                        {service.image && (
                          <img
                            src={service.image}
                            alt={service.title}
                            className="absolute inset-0 w-full h-full object-cover opacity-40 group-hover:opacity-60 transition-opacity duration-500"
                          />
                        )}
                        
                        {/* Gradient Overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-brand-purple-900/90 via-brand-purple-800/60 to-transparent" />
                        
                        {/* Content */}
                        <div className="relative z-10 p-8 h-full flex flex-col justify-between">
                          <div>
                            <div className="flex items-center mb-4">
                              <div className="bg-brand-purple-600 p-3 rounded-2xl mr-4">
                                {(() => {
                                  const IconComponent = getServiceIcon(index);
                                  return <IconComponent className="w-8 h-8 text-white" />;
                                })()}
                              </div>
                              <h2 className="text-3xl font-bold text-white group-hover:text-brand-purple-200 transition-colors duration-300">
                                {service.title}
                              </h2>
                            </div>
                            <p className="text-brand-purple-100 text-lg leading-relaxed mb-6">
                              {service.description}
                            </p>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-brand-purple-300 font-semibold">
                              {service.subServices?.length || 0} Sub-Services Available
                            </span>
                            <div className="flex items-center space-x-2 text-brand-purple-300">
                              <span className="text-sm">Click to explore</span>
                              <ChevronDown className={`w-5 h-5 transition-transform duration-300 ${selectedService === service.id ? 'rotate-180' : ''}`} />
                            </div>
                          </div>
                        </div>
                        
                        {/* Glow effect */}
                        <div className="absolute inset-0 rounded-3xl ring-2 ring-brand-purple-500/0 group-hover:ring-brand-purple-500/50 transition-all duration-500" />
                      </div>
                    </div>
                  </div>

                  {/* Sub-Services Grid */}
                  <div className="flex-1 max-w-2xl">
                    <AnimatePresence>
                      {selectedService === service.id && service.subServices && service.subServices.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          transition={{ duration: 0.5 }}
                          className="space-y-6"
                        >
                          <div className="text-center mb-8">
                            <h3 className="text-2xl font-bold text-white mb-2">
                              {service.title} <span className="text-brand-purple-400">Sub-Services</span>
                            </h3>
                            <div className="w-16 h-1 bg-gradient-to-r from-brand-purple-600 to-brand-purple-800 mx-auto rounded-full" />
                          </div>
                          
                          <div className="grid grid-cols-1 gap-4">
                            {service.subServices.map((subService, subIndex) => (
                              <SubServiceCard
                                key={`${service.id}-${subIndex}`}
                                subService={subService}
                                serviceName={service.title}
                                index={subIndex}
                              />
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Services;
